<template>
    <div class="perform-box">
        <div ref="wrapper" class="wrapper-box">
            <grid-layout
                :layout.sync="layout"
                :col-num="colNum"
                :row-height="rowHeight"
                :is-draggable="false"
                :is-resizable="false"
                :is-mirrored="false"
                :vertical-compact="true"
                :margin="[4, 10]"
                :use-css-transforms="true"
                :style="gridItemStyle">
                <grid-item
                    v-for="(item, idx) in layout"
                    :key="item.i"
                    class="service-box"
                    :x="item.x"
                    :y="item.y"
                    :w="item.w"
                    :h="item.h"
                    :i="item.i">
                    <p class="business-box-title" :title="panels[idx].describe || '-'">
                        {{ panels[idx].describe || '-' }}
                    </p>
                    <!-- 指标总览 -->
                    <apm-scroll ref="apmScroll" :style="{ height: 'calc(100% - 21px)' }">
                        <info-sum-bar
                            :data="panels[idx].performOverview"
                            :selectInfoId="selectInfoId"
                            selectedStyleType="border"
                            @info-bar-click="handleBarClick">
                        </info-sum-bar>
                    </apm-scroll>
                </grid-item>
            </grid-layout>
            <no-data v-if="!panels.length" />
            <a-loading v-if="loading" style="width: 100%; height: 100%;"></a-loading>
        </div>
        <!-- 侧边弹窗 -->
        <data-accord-drawer
            v-if="drawerInfo.status"
            ref="accord-drawer"
            :modalInfo="drawerInfo"
            @drawer-close="handleDrawerClose"
        ></data-accord-drawer>
    </div>
</template>

<script>

import { GridLayout, GridItem } from 'vue-grid-layout';
import aLoading from '@/components/common/loading/aLoading';
import infoSumBar from '@/components/common/infoBar/infoSumBar';
import noData from '@/components/common/noData/noData';
import apmScroll from '@/components/common/bestScroll/apmScroll';
import _ from 'lodash';
import { isDivisible } from '@/utils/utils';
import dataAccordDrawer from './dataAccordDrawer.vue';
import { getProductObservation } from '@/api/topoApi';
import { getObservableTransactionInfo } from '@/api/httpApi';
const TIMER_INTERVAL = 10;
export default {
    props: {
        productInstNo: {
            type: String,
            default: ''
        }
    },
    components: {
        GridLayout, GridItem, apmScroll,
        aLoading, noData,
        infoSumBar,
        dataAccordDrawer
    },
    data() {
        return {
            loading: false,
            layout: [],
            colNum: 12,
            panels: [],
            rowHeight: 0,
            selectInfoId: '',
            // 指标值
            indicatorsData: [
                {
                    label: '主核心事务数',
                    key: 'masterCommitSqn'
                },
                {
                    label: '备核心事务最大差量',
                    key: 'standbyMaxCommitSqnDiff'
                },
                {
                    label: '回库事务最大差量',
                    key: 'todbMaxCommitSqnDiff'
                }
            ],
            indicators: [],
            timer: null,
            // ------------------------- 弹窗数据 -------------------------
            drawerInfo: {
                status: false
            }
        };
    },
    computed: {
        gridItemStyle() {
            return { minWidth: this.panels.length * 450 + 'px' };
        }
    },
    mounted() {
        window.addEventListener('resize', this.resize);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.resize);
    },
    methods: {
        // 初始化
        async init() {
            this.clearPolling();
            this.cleanData();
            this.loading = true;

            // 开启定时器
            this.setPolling(TIMER_INTERVAL);

            const panels = await this.handleShardTabs();
            this.indicators = await this.getObservableTransactionInfo(panels);
            await this.getPerformanceData(panels);
            this.generateLayout(panels);
            this.loading = false;
            this.resetLayout();
        },
        // 手动清理页面数据
        cleanData(){
            this.selectInfoId = '';
            this.indicators = [];
        },
        // 重置layout
        resetLayout() {
            this.layoutStu = false;
            this.$nextTick(() => {
                this.rowHeight = parseInt(this.$refs.wrapper?.clientHeight / 10 - 5 * 2, 10);
                this.layoutStu = true;
            });
        },
        // 定时器
        setPolling(timerInterval) {
            this.clearPolling();
            this.timer = setInterval(async () => {
                const panels = await this.handleShardTabs();
                this.indicators = await this.getObservableTransactionInfo(panels);
                await this.getPerformanceData(panels);
            }, (timerInterval) * 1000);
        },
        // 清空定时器
        clearPolling() {
            this.timer && clearInterval(this.timer);
        },
        // 点击事件
        async handleBarClick(id) {
            this.selectInfoId = id;
            const [serviceCode, selectInsId, dataType] = id?.split('#');
            // 弹窗数据
            const serviceCodePanel = _.find(this.panels, ['id', serviceCode]) || {};
            const details = _.find(serviceCodePanel.performOverview.details, ['infoId', `${serviceCode}#${selectInsId}`]) || {};
            this.handleDrawerOpen(details?.instances, dataType, details?.shardingNo);
        },
        // 获取全部集群同步数据
        async getObservableTransactionInfo(panels) {
            const indicators = [];
            const params = {
                productId: this.productInstNo
            };
            try {
                const res = await getObservableTransactionInfo(params);
                if (res.code === '200') {
                    const list = res?.data || [];
                    panels.forEach(item => {
                        item.appClusters.forEach(cluster => {
                            const indicator = list.find(o => o.clusterId === cluster.id);
                            if (indicator) {
                                indicators.push({
                                    ...indicator,
                                    clusterName: cluster.clusterName || '未知',
                                    shardingNo: cluster.shardingNo,
                                    serviceCode: item.id
                                });
                            }
                        });

                    });
                }
            } catch (err) {
                this.clearPolling();
            }
            return indicators;
        },
        // 获取模板接口
        async handleShardTabs() {
            // 应用节点观测仪表盘
            const param = {
                productId: this.productInstNo,
                observationMode: 'service',
                instanceIdentity: 'bizproc'
            };
            return new Promise((resolve, reject) => {
                getProductObservation(param).then(res => {
                    let panels = [];
                    if (res.success) {
                        panels = this.generatePanels(res?.data?.services || []);
                        resolve(panels);
                    } else {
                        resolve(false);
                    }
                }).catch(error => {
                    this.clearPolling();
                    reject(error);
                    console.error(error);
                });
            });
        },
        // 生成面板
        generatePanels(list) {
            const panels = [];
            list.forEach(item => {
                const data = {
                    id: item.serviceCode,
                    describe: item.serviceName || '未知',
                    appClusters: item.appClusters || [],
                    clusterIds: item.appClusters.map(o => o.id),
                    performOverview: {
                        direction: 'grid',
                        autoGrid: true,
                        gridMinWidth: '420px',
                        details: []
                    }
                };
                panels.push(data);
            });
            return panels;
        },
        // 生成模型
        generateLayout(panels) {
            this.colNum = isDivisible(panels.length);
            const w = this.colNum / panels.length;
            this.layout = [];
            this.panels = [];
            panels.forEach((item, index) => {
                this.layout.push({
                    x: index * w,
                    y: 0,
                    w,
                    h: 10,
                    i: item.id?.toString()
                });
                this.panels.push({
                    ...item
                });
            });
        },
        // 构建info组件数据
        setIndicatorsInfoData(serviceCode, indicators){
            const infoList = [];
            for (const indicator of Object.values(indicators)){
                infoList.push({
                    type: 'monitor',
                    title: {
                        label: `分片 ${indicator.shardingNo || '-'}`,
                        noTagColor: true,
                        slots: [
                            {
                                type: 'text', // 文本
                                value: indicator.clusterName || '-'
                            }
                        ]
                    },
                    canClick: false,
                    infoId: `${serviceCode}#${indicator.clusterId}`,
                    info: this.setIndicators(serviceCode, indicator),
                    shardingNo: indicator?.shardingNo,
                    instances: indicator?.instances || [] // 弹窗数据
                });
            }
            return infoList;
        },
        // 指定指标
        setIndicators(serviceCode, indicator){
            const info = [];
            Object.values(this.indicatorsData).forEach(data => {
                info.push(
                    {
                        type: 'text',
                        label: data.label,
                        canClick: true,
                        infoId: `${serviceCode}#${indicator.clusterId}#${data.key}`,
                        key: data.key,
                        value: indicator[data?.key]
                    }
                );
            });
            return info;
        },
        // 展示指标数据以及指标图表
        async getPerformanceData(panels) {
            panels.forEach(item => {
                // 指标数据按照分片过滤
                let indicators = [];
                indicators = this.indicators?.filter(o =>
                    o.serviceCode === item?.id
                );
                item.performOverview.details = this.setIndicatorsInfoData(item?.id, indicators);
                if (item.performOverview.details.length) {
                // 获取选中模块Id
                    const [serviceCode, selectInsId, dataType] = this.selectInfoId?.split('#');
                    const selectIns = _.find(item.performOverview.details, ['infoId', `${serviceCode}#${selectInsId}`]) || {};
                    const id = _.find(selectIns?.info, ['infoId', this.selectInfoId])?.infoId;
                    id && this.updateDrawerData(id);
                }
            });

        },

        // ----------------------------------------DrawerBox-------------------------------------------------------------
        // 查看侧弹窗信息
        handleDrawerOpen(data, dataType, shardingNo) {
            this.drawerInfo.status = true;
            this.drawerInfo.insData = data;
            this.drawerInfo.dataType = dataType;
            this.drawerInfo.shardingNo = shardingNo;
        },
        // 轮询设置弹窗数据
        updateDrawerData(id) {
            if (this.selectInfoId !== id) return;
            const [serviceCode, selectInsId, dataType] = id?.split('#');
            // 弹窗数据
            const serviceCodePanel = _.find(this.panels, ['id', serviceCode]) || {};
            const instances = _.find(serviceCodePanel.performOverview.details, ['infoId', `${serviceCode}#${selectInsId}`])?.instances || [];
            this.drawerInfo.insData = instances;
            this.drawerInfo.dataType = dataType;
            if (this.$refs['accord-drawer']) {
                this.$refs['accord-drawer'].handleDataUpdate();
            }
        },
        // 关闭弹窗
        handleDrawerClose() {
            this.selectInfoId = '';
        }
    }
};
</script>
  <style lang="less" scoped>
.perform-box {
    width: 100%;
    height: 100%;

    .wrapper-box {
        width: 100%;
        height: 100%;
        position: relative;
        overflow-x: auto;
        overflow-y: hidden;
    }

    .service-box {
        padding-right: 3px;
        border-right: 1px solid #9797970e;
        height: 100%;
    }

    .business-box-title {
        position: relative;
        width: 100%;
        height: 32px;
        margin: 0 auto;
        background: linear-gradient(to right, #202637, #2f3959, #202637);
        background-repeat: no-repeat;
        top: -8px;
        text-align: center;
        line-height: 32px;
        color: var(--font-color);
        font-size: 14px;

        span {
            display: inline-block;
            max-width: calc(100% - 20px);
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    }

    .info-sum-bar {
        margin-top: 0;
        background: none;

        /deep/ .info-bar {
            &:hover {
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.5);
                cursor: default;
            }
        }
    }

    /deep/ .obs-title {
        background: var(--primary-color);
    }
}

  </style>

