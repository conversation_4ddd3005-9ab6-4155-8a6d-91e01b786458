<!--
 * @Description: 注册信息页面
 * @Author: Generated
 * @Date: 2025-08-15
-->
<template>
    <div class="main">
        <query-table
            ref="queryTable"
            title="注册信息"
            :loading="loading"
            :formItems="formItems"
            :formCols="4"
            :tableData="tableData"
            :columnData="columnData"
            :total="total"
            :hasPage="true"
            :hasFilter="true"
            :showTitle="true"
            @query="handleQuery"
            @reset="handleReset">

            <!-- 标题区插槽 -->
            <template v-slot:tableTitle>
                <span class="record-count">
                    注册节点总数：{{ total }}
                </span>
            </template>

            <!-- 操作区插槽 -->
            <template v-slot:operateBlock>
                <a-button type="primary" @click="handleRefresh">
                    <h-icon name="refresh"></h-icon>
                    刷新
                </a-button>
                <a-button type="dark" @click="handleExport">
                    <h-icon name="download"></h-icon>
                    导出
                </a-button>
            </template>
        </query-table>
    </div>
</template>

<script>
import queryTable from '@/components/common/bestTable/queryTable/queryTable';
import aButton from '@/components/common/button/aButton';
import { formatDate } from '@/utils/utils';

export default {
    name: 'RegistrationInfo',
    components: {
        queryTable,
        aButton
    },
    data() {
        return {
            loading: false,
            tableData: [],
            total: 0,
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                nodeId: '',
                ip: '',
                port: '',
                shardId: '',
                healthStatus: ''
            },
            // 查询表单配置
            formItems: [
                {
                    type: 'input',
                    key: 'nodeId',
                    label: '节点ID',
                    placeholder: '请输入节点ID'
                },
                {
                    type: 'input',
                    key: 'ip',
                    label: 'IP',
                    placeholder: '请输入IP地址'
                },
                {
                    type: 'input',
                    key: 'port',
                    label: '端口',
                    placeholder: '请输入端口号'
                },
                {
                    type: 'select',
                    key: 'shardId',
                    label: '所属分片',
                    placeholder: '请选择分片',
                    options: [
                        { label: '全部', value: '' },
                        { label: '分片61', value: '61' },
                        { label: '分片62', value: '62' },
                        { label: '分片63', value: '63' }
                    ]
                },
                {
                    type: 'select',
                    key: 'healthStatus',
                    label: '健康状态',
                    placeholder: '请选择状态',
                    options: [
                        { label: '全部', value: '' },
                        { label: '在线', value: 'online' },
                        { label: '离线', value: 'offline' }
                    ]
                }
            ],
            // 表格列配置
            columnData: [
                {
                    title: '节点ID',
                    key: 'nodeId',
                    width: 120,
                    align: 'center',
                    render: (h, params) => {
                        return h('span', {
                            style: {
                                color: '#409eff',
                                cursor: 'pointer'
                            },
                            on: {
                                click: () => this.showDetail(params.row)
                            }
                        }, params.row.nodeId);
                    }
                },
                {
                    title: 'IP',
                    key: 'ip',
                    width: 140,
                    align: 'center'
                },
                {
                    title: '端口',
                    key: 'port',
                    width: 80,
                    align: 'center'
                },
                {
                    title: '所属分片',
                    key: 'shardId',
                    width: 100,
                    align: 'center'
                },
                {
                    title: '健康状态',
                    key: 'healthStatus',
                    width: 100,
                    align: 'center',
                    render: (h, params) => {
                        const status = params.row.healthStatus;
                        const isOnline = status === 'online';
                        return h('span', {
                            class: isOnline ? 'status-online' : 'status-offline'
                        }, [
                            h('span', {
                                class: 'status-dot',
                                style: {
                                    backgroundColor: isOnline ? '#52c41a' : '#ff4d4f'
                                }
                            }),
                            isOnline ? '在线' : '离线'
                        ]);
                    }
                },
                {
                    title: '最后心跳时间',
                    key: 'lastHeartbeatTime',
                    width: 180,
                    align: 'center',
                    render: (h, params) => {
                        return h('span', formatDate(params.row.lastHeartbeatTime, 'YYYY-MM-DD HH:mm:ss'));
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    width: 120,
                    align: 'center',
                    render: (h, params) => {
                        return h('div', [
                            h('a-button', {
                                props: {
                                    type: 'text',
                                    size: 'small'
                                },
                                style: {
                                    marginRight: '8px'
                                },
                                on: {
                                    click: () => this.showDetail(params.row)
                                }
                            }, '详情'),
                            h('a-button', {
                                props: {
                                    type: 'text',
                                    size: 'small'
                                },
                                on: {
                                    click: () => this.handlePing(params.row)
                                }
                            }, '测试连接')
                        ]);
                    }
                }
            ],
            // 详情弹窗
            detailModal: {
                visible: false,
                data: {}
            }
        };
    },
    mounted() {
        this.initData();
    },
    methods: {
        // 初始化数据
        async initData() {
            await this.getTableData();
        },

        // 获取表格数据
        async getTableData() {
            try {
                this.loading = true;
                const res = await getRegistrationList(this.queryParams);
                if (res.code === '200') {
                    this.tableData = res.data.list || [];
                    this.total = res.data.total || 0;
                }
            } catch (error) {
                console.error('获取注册信息失败:', error);
                this.$hMessage.error('获取注册信息失败');
            } finally {
                this.loading = false;
            }
        },

        // 查询事件
        handleQuery(params) {
            this.queryParams = {
                ...this.queryParams,
                ...params.formData,
                pageNum: params.pageNum || 1,
                pageSize: params.pageSize || 10
            };
            this.getTableData();
        },

        // 重置事件
        handleReset() {
            this.queryParams = {
                pageNum: 1,
                pageSize: 10,
                nodeId: '',
                ip: '',
                port: '',
                shardId: '',
                healthStatus: ''
            };
            this.getTableData();
        },

        // 刷新数据
        handleRefresh() {
            this.getTableData();
        },

        // 导出数据
        async handleExport() {
            try {
                this.loading = true;
                const res = await exportRegistrationData(this.queryParams);
                if (res.code === '200') {
                    // 处理文件下载
                    const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' });
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `注册信息_${formatDate(new Date(), 'YYYY-MM-DD_HH-mm-ss')}.xlsx`;
                    link.click();
                    window.URL.revokeObjectURL(url);
                    this.$hMessage.success('导出成功');
                }
            } catch (error) {
                console.error('导出失败:', error);
                this.$hMessage.error('导出失败');
            } finally {
                this.loading = false;
            }
        },

        // 显示详情
        showDetail(row) {
            this.detailModal = {
                visible: true,
                data: row
            };
        },

        // 测试连接
        async handlePing(row) {
            try {
                this.$hMessage.info('正在测试连接...');
                // 这里可以调用测试连接的API
                // const res = await pingNode({ nodeId: row.nodeId });
                // 模拟测试结果
                setTimeout(() => {
                    this.$hMessage.success(`节点 ${row.nodeId} 连接正常`);
                }, 1000);
            } catch (error) {
                this.$hMessage.error(`节点 ${row.nodeId} 连接失败`);
            }
        }
    }
};
</script>

<style lang="less" scoped>
.main {
    width: 100%;
    height: 100%;

    .record-count {
        color: var(--font-color);
        font-size: 14px;
        margin-left: 16px;
    }

    /deep/ .status-online,
    /deep/ .status-offline {
        display: flex;
        align-items: center;

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 6px;
        }
    }

    /deep/ .status-online {
        color: #52c41a;
    }

    /deep/ .status-offline {
        color: #ff4d4f;
    }
}
</style>
