<template>
  <div ref="table-box" class="table-box">
    <obs-table
      v-if="hasLatestExport"
      showTitle
      isSimpleTable
      border
      canDrag
      :title="tableTitle"
      :columns="columns"
      :tableData="tableData"
      :loading="tableLoading"
      :height="tableHeight"
    >
      <template slot="extraTitleBox">
        <div class="title-slots">
          <div class="title-wrap">
            <div class="title-content">
              <span class="main-title">最近一次导出</span>
              <span class="separator1"></span>
              <span
                class="indicator"
                :style="{ backgroundColor: statusIndicator.color }"
              >
              </span>
              <span>{{ statusIndicator.text || "-" }}</span>
              <span class="separator2"></span>
              <span>
                导出时间：{{ taskDetail.gmCreate || " " }}； 需导出表数量
                {{ taskDetail.tableTotalCount }} （成功
                {{ taskDetail.successNum }} / 失败 {{ taskDetail.failedNum }}）
              </span>
              <!-- 失败原因 -->
              <!-- <h-poptip
                v-if="execBatchStatus === 'failed'"
                trigger="click"
                customTransferClassName="apm-poptip monitor-poptip"
                autoPlacement
                :content="taskDetail.errorMsg"
                transfer
              >
                <span style="color: var(--link-color); cursor: pointer;"
                  >&nbsp;&nbsp;失败原因</span
                >
              </h-poptip> -->
            </div>
            <div class="title-action">
              <a-button
                type="dark"
                :disabled="downloadDisabled"
                :loading="downloading"
                style="margin-right: 8px;"
                @click="handleDownloadAll"
              >
                下载
              </a-button>
              <a-button type="primary" @click="handleCreateTask">
                创建任务
              </a-button>
            </div>
          </div>
        </div>
      </template>
    </obs-table>
    <apm-blank v-else name="Data">
            <slot>
                <div>暂无数据</div>
                <br />
                <a-button
                    type="primary"
                    @click="handleCreateTask">
                    创建任务
                </a-button>
            </slot>
        </apm-blank>
  </div>
</template>

<script>
import obsTable from '@/components/common/obsTable/obsTable';
import aButton from '@/components/common/button/aButton';
import apmBlank from '@/components/common/apmBlank/apmBlank';
import { getLatestExportData, downloadMdb } from '@/api/mdbExportApi';
import { STATUS_COLOR_MAP } from './constant';
import importStatusTableIcon from '@/components/secondAppearance/importStatusTableIcon.vue'; // 有用
import { dateFormat, getCurrentDatetime } from '@/utils/utils';
import { MDB_NO_LOGIN } from '@/config/errorCode';

export default {
    components: {
        obsTable,
        aButton,
        apmBlank
    },
    props: {
        productId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            hasLatestExport: false,
            // 上场任务详情
            execBatchStatus: '',
            taskId: null,
            taskDetail: {
                gmCreate: '',
                endTime: '',
                tableTotalCount: '',
                successNum: '',
                failedNum: '',
                errorMsg: ''
            },

            // 表格
            tableLoading: false,
            tableHeight: 300,
            downloading: false,
            tableTitle: {},
            columns: [
                {
                    title: '服务器',
                    key: 'hostName',
                    sortable: true,
                    ellipsis: true
                },
                {
                    title: '节点',
                    key: 'instanceName',
                    sortable: true,
                    ellipsis: true
                },
                {
                    title: '表名',
                    key: 'tableName',
                    sortable: true,
                    ellipsis: true
                },
                {
                    title: '集群',
                    key: 'clusterName',
                    ellipsis: true,
                    sortable: true
                },
                {
                    title: '分片',
                    key: 'shardNo',
                    ellipsis: true,
                    maxWidth: 80,
                    sortable: true
                },
                {
                    title: '远程路径',
                    key: 'remoteExportPath',
                    ellipsis: true
                },
                {
                    title: '导出状态和结果',
                    maxWidth: 140,
                    ellipsis: true,
                    render: (_, { row }) => {
                        let iconType, text;
                        switch (row.status) {
                            case 'exporting':
                                iconType = 'loading';
                                text = '导出中';
                                break;
                            case 'finished':
                                iconType = 'success';
                                text = '导出成功';
                                // text = `记录总数 ${row.recordTotalCount}（成功 ${row.recordSuccessCount} / 失败 ${row.recordFailCount}）`;
                                break;
                            case 'partSuccess':
                                iconType = 'warn';
                                // text = `记录总数 ${row.recordTotalCount}（成功 ${row.recordSuccessCount} / 失败 ${row.recordFailCount}）`;
                                text = '部分成功';
                                break;
                            case 'error':
                                iconType = 'error';
                                text = '导出失败';
                                break;
                            case 'waiting':
                                iconType = 'offline';
                                text = '待导出';
                                break;
                        }
                        return (
                            <div title={text} class="h-table-cell-ellipsis">
                                <importStatusTableIcon type={iconType} />
                                {row.status === 'error' && row.errorMsg ? (
                                    <h-poptip
                                        customTransferClassName="apm-poptip"
                                        transfer
                                        autoPlacement
                                        content={row.errorMsg}
                                        trigger="click"
                                    >
                                        <span class="click-text hover-underline">导出失败</span>
                                    </h-poptip>
                                ) : (
                                    text
                                )}
                            </div>
                        );
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    width: 110,
                    // fixed: 'right',
                    // render: (h, params) => {
                    //     const canDownload = [
                    //         STATUS_COLOR_MAP.finished.value
                    //     ].includes(params.row.status);
                    //     return h('span', [
                    //         h(
                    //             'a',
                    //             {
                    //                 style: {
                    //                     cursor: canDownload ? 'pointer' : 'not-allowed'
                    //                 },
                    //                 on: {
                    //                     click: () => {
                    //                         if (!canDownload) return;
                    //                         this.download(
                    //                             {
                    //                                 instanceId: params.row.instanceId,
                    //                                 tableName: params.row.tableName
                    //                             },
                    //                             `${params.row.tableName}_${getCurrentDatetime()}.zip`
                    //                         );
                    //                     }
                    //                 }
                    //             },
                    //             '下载'
                    //         )
                    //     ]);
                    // },
                    render: (_, { row }) => {
                        const canDownload = [
                            STATUS_COLOR_MAP.finished.value
                        ].includes(row.status);
                        return (
                            <span>
                                {
                                    row.loading && <importStatusTableIcon type="loading" />
                                }
                                <a onClick={async () => {
                                    if (!canDownload || row.loading) return;
                                    const key = `${row.tableName}${row.instanceId}`;
                                    this.tableData = this.tableData.map(item => ({
                                        ...item,
                                        loading: key ===  `${item.tableName}${item.instanceId}`
                                    }));
                                    try {
                                        await this.download(
                                            {
                                                instanceId: row.instanceId,
                                                tableName: row.tableName
                                            },
                                            `${row.tableName}_${getCurrentDatetime()}.zip`
                                        );
                                    } finally {
                                        row.loading = false;
                                        this.tableData = this.tableData.map(item => ({
                                            ...item,
                                            loading: false
                                        }));
                                    }
                                }} class={canDownload && !row.loading ? 'pointer' : 'not-allowed'} style={{ cursor: canDownload && !row.loading ? 'pointer' : 'not-allowed' }}>下载</a>
                            </span>
                        );
                    }
                }
            ],
            tableData: []
        };
    },
    computed: {
        statusIndicator() {
            return (
                STATUS_COLOR_MAP[this.execBatchStatus] || {
                    color: '',
                    text: ''
                }
            );
        },
        // 导出任务执行前或执行中，禁止创建任务
        createBtnDisabled() {
            const canCreate = [STATUS_COLOR_MAP.exporting.value].includes(
                this.execBatchStatus
            );
            return !canCreate;
        },
        /**
     * 能否下载
     */
        downloadDisabled() {
            if (!this.tableData.length) return true;
            const canDownload = [
                STATUS_COLOR_MAP.finished.value,
                STATUS_COLOR_MAP.partSuccess.value
            ].includes(this.execBatchStatus);
            return !canDownload;
        }
    },
    async mounted() {
        window.addEventListener('resize', this.fetTableHeight);
        this.fetTableHeight();
        // 开启定时器
        this.handleSetInterval();
    },
    beforeDestroy() {
    // 清除定时器和窗口大小监听事件
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
    /**
     * @description 初始化数据，设置 loading 状态，调用查询数据接口
     */
        async initData() {
            this.tableLoading = true;
            try {
                await this.queryExportData();
            } catch (err) {
                console.error(err);
            } finally {
                this.tableLoading = false;
            }
        },
        /**
     * @description 定时查询上场状态并更新UI
     *
     * 该方法会每隔5秒调用后端接口查询当前导出状态，并根据状态更新UI界面。
     * 用于在“pending”（待）或“running”（运行中）状态时，不断刷新当前状态信息。
     */
        handleSetInterval() {
            // 每隔 5s 调用后端接口最近导出状态,并更新 UI 界面
            this.timer = setInterval(async () => {
                if (['exporting'].includes(this.execBatchStatus)) {
                    await this.queryExportData(true);
                }
            }, 5000);
        },
        /**
     * @description 调用接口，查询导出状态，更新任务详情
     */
        async queryExportData(ignoreRefulshDetail = false) {
            let hasLatestExport = false;
            const params = { productId: this.productId };
            this.taskId = null;
            // if (ignoreRefulshDetail) this.taskDetail = {};
            try {
                const res = await getLatestExportData(params);
                if (res?.code === MDB_NO_LOGIN) {
                    this.callResetLogin();
                    return;
                }
                if (res?.code === '200') {
                    const data = res?.data;
                    if (data) {
                        hasLatestExport = true;
                        const {
                            status,
                            time,
                            successNum,
                            failedNum,
                            errorMsg,
                            infos,
                            taskId,
                            totalCount
                        } = data;
                        this.execBatchStatus = status;
                        // let tableTotalCount = '-';
                        // if (!isNaN(successNum) && !isNaN(failedNum)) {
                        //     tableTotalCount = Number(successNum) + Number(failedNum);
                        // }
                        this.taskDetail = {
                            gmCreate: time ? dateFormat(time, 'YYYY-MM-DD HH:MM:ss') : '-',
                            successNum,
                            tableTotalCount: totalCount,
                            failedNum,
                            errorMsg
                        };
                        this.tableData = infos || [];
                        this.taskId = taskId;
                    } else {
                        this.taskDetail = {};
                    }
                } else {
                    this.handleErrorState(res.message);
                }
            } catch (e) {
                this.handleErrorState();
            } finally {
                this.hasLatestExport = hasLatestExport;
            }
        },
        /**
     * token过期，登出
     */
        callResetLogin() {
            this.$emit('resetLogin');
        },
        /**
     * @description 错误状态处理函数
     * @param {String} message - 错误信息
     */
        handleErrorState(message) {
            this.taskDetail = {};
            this.tableData = [];
            message && this.$hMessage.error(message);
            if (this.timer) {
                clearInterval(this.timer);
                this.timer = null;
            }
        },
        /**
     * @description 动态设置表格高度的处理函数
     */
        fetTableHeight() {
            this.tableHeight =
        this.$refs['table-box']?.getBoundingClientRect().height - 60;
        },
        /**
     * @description 触发创建任务事件
     */
        handleCreateTask() {
            this.$emit('goCreate');
        },
        /**
     * 下载单张表
     */
        async download(param, name) {
            try {
                const res = await downloadMdb({ ...param, productId: this.productId, taskId: this.taskId  });
                if (res?.code === MDB_NO_LOGIN) {
                    this.callResetLogin();
                    return;
                }
                // blob类型 text/xml
                if (res?.type !== 'application/json') {
                    const url = URL.createObjectURL(res); // 生成临时下载链接
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = name;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);
                    this.$hMessage.success('下载成功!');
                } else if (res?.type === 'application/json') {
                    const text = await res.text();
                    const json = JSON.parse(text);
                    this.$hMsgBoxSafe.error({
                        title: '下载失败',
                        content: json?.message
                    });
                }
            } catch (error) {
                console.log('下载单张表失败,', error);
            }
        },
        /**
     * 全量下载
     */
        async handleDownloadAll() {
            if (!this.taskId) return;
            this.downloading = true;
            try {
                await this.download({ taskId: this.taskId }, `${this.taskId}_${getCurrentDatetime()}.zip`);
            } catch (error) {
                // ignore
            } finally {
                this.downloading = false;
            }
        }
    }
};
</script>
<style lang="less">
@import url("@/assets/css/poptip-1.less");

.apm-poptip .h-poptip-inner .h-poptip-body {
    .h-poptip-body-content {
        max-width: 400px;
        max-height: 200px;
        white-space: normal;
        word-break: break-all;
    }

    .h-poptip-body-content-inner {
        color: var(--font-color);
    }
}

.hover-underline:hover {
    text-decoration: underline;
    text-decoration-color: #2d8de5;
}
</style>

<style lang="less" scoped>
/* 组件样式 */
.table-box {
    height: calc(100% - 60px);
    padding: 0 5px;

    .obs-table {
        background-color: unset;
    }

    /deep/ .h-table td {
        background-color: unset;
    }

    .not-allowed {
        color: #969797;

        &:hover {
            cursor: not-allowed;
            color: #969797;
        }
    }
}

.title-slots {
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
    width: calc(100% - 30px);
    margin-left: 10px;

    .title-wrap {
        width: 100%;
        display: flex;
        justify-content: space-between;
    }

    .title-content {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .main-title {
        font-size: 14px;
    }

    .indicator {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        margin-right: 9px;
    }

    .separator1 {
        border-left: 1px solid #474e6f;
        height: 20px;
        margin: 0 15px 0 12px;
    }

    .separator2 {
        border-left: 1px solid #474e6f;
        height: 12px;
        margin: 0 12px;
    }
}

.click-text {
    color: var(--link-color);

    &:hover {
        cursor: pointer;
    }
}
</style>
