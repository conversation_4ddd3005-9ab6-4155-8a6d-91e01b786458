<template>
    <div>
        <h-msg-box-safe
            v-model="modalData.status"
            :mask-closable="false"
            :escClose="false"
            title="批量导出"
            width="1000"
            height="425"
            :allowCopy="true"
            @on-open="handleOpen"
        >
            <div class="batch-box">
                <div class="batch-box-title">
                    <div class="batch-box-left">
                        <div class="batch-title">
                            搜索管理功能 ({{ leftData.length }})
                        </div>
                        <div class="btach-search">
                            <h-simple-select
                                v-model="selectManageName"
                                placeholder="请输入管理功能名称搜索"
                                filterable
                                :loading="isLoading"
                                style="width: 180px; margin-right: 2px;"
                                @on-change="handleSearchChange"
                                >
                                <h-select-block :data="remoteData"></h-select-block>
                            </h-simple-select>
                            <h-select
                                v-model="selectInstanceList"
                                collapseTags
                                multClearable
                                multiple
                                isCheckall
                                placeholder="请选择节点"
                                style="width: 200px; margin-right: 2px;">
                                <h-option
                                    v-for="item in instanceList"
                                    :key="item.instanceName"
                                    :value="item.instanceName"
                                    collapseTags
                                    >{{ item.instanceName }}</h-option>
                            </h-select>
                            <h-button
                                type="primary"
                                @click="handleSearch">查询</h-button>
                        </div>
                    </div>
                    <div class="batch-box-right">
                        <div class="batch-title">
                            待导出列表 ({{ rightData.length }})
                        </div>
                        <div
                            v-if="exportStatus"
                            class="export-run-box"
                            :class="['export-run-box-' + exportStatus]">
                            <span
                                class="export-run-text">
                                {{statusDesc[exportStatus]}}({{ lastExportTime }})
                            </span>
                            <span>|</span>
                            <a
                                v-if="exportStatus === 'SUCCESS'"
                                class="export-run-opt"
                                @click="downloadManageFile">下载</a>
                            <a
                                v-if="exportStatus === 'RUNNING'"
                                class="export-run-opt"
                                @click="handleStopExport">终止</a>
                            <h-poptip
                                trigger="hover"
                                class="apm-poptip-time title-btn"
                                customTransferClassName="apm-poptip apm-poptip-time monitor-poptip"
                                placement="bottom-end"
                                style="color: #fff;">
                                <a
                                    v-if="exportStatus === 'FAILED' || exportStatus === 'STOPPED'"
                                    class="export-run-opt">原因</a>
                                <div slot="content" style='white-space: normal;'>
                                    {{ exportErrorMsg }}
                                </div>
                            </h-poptip>
                        </div>
                        <span v-if="exportStatus === 'RUNNING' || exportStatus === 'SUCCESS'">{{ process }}%</span>
                        <h-button
                            style="float: right; margin-top: 10px;"
                            @click="clearRightData">清空列表</h-button>
                    </div>
                </div>
                <h-transfer-table
                    :lWidth="450"
                    :rWidth="450"
                    :height="320"
                    hiderTable
                    :lColumns="lColumns"
                    :rColumns="rColumns"
                    :lData="leftData"
                    :rData="rightData"
                    :showTitle="false"
                    :border="false"
                    :canDrag="false"
                    :loading="{
                        left: loadingLeft,
                        right: false
                    }"
                    @on-change="handleTransferChange"
                    ></h-transfer-table>
                <h-simple-table
                    :columns="rColumns"
                    :data="rightData"
                    no-data-text="暂无数据"
                    :show-header="true"
                    :highlight-row="true"
                    :disabled-hover="true"
                    :height="320"
                    style="width: 450px; position: absolute; top: 64px; right: 0;"
                    ></h-simple-table>
            </div>
            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button
                    type="primary"
                    :disabled="!rightData.length"
                    :loading="exportStatus === 'RUNNING'"
                    @click="submitConfig">导出</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import _ from 'lodash';
import aButton from '@/components/common/button/aButton';
import { getManageInfoAllProduct, exportManageApi, getManageExportProgress, downloadManageFile, stopDownloadManage } from '@/api/httpApi';
export default {
    name: 'ExportDataModal',
    props: {
        productId: {
            type: String,
            default: ''
        },
        modalData: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            modalInfo: {},
            selectManageName: '',
            loading: false,
            selectInstanceList: [],
            remoteData: [],
            lColumns: [
                {
                    type: 'selection',
                    width: 40
                },
                {
                    title: '管理功能',
                    width: 150,
                    key: 'name',
                    ellipsis: true,
                    render: (h, params) => {
                        return h('span', {
                            attrs: {
                                title: params.row.name
                            }
                        }, params.row.name);
                    }
                },
                {
                    width: 120,
                    title: '节点',
                    key: 'instanceName',
                    ellipsis: true,
                    render: (h, params) => {
                        return h('span', {
                            attrs: {
                                title: params.row.instanceName
                            }
                        }, params.row.instanceName);
                    }
                },
                {
                    width: 130,
                    title: '插件',
                    key: 'pluginName',
                    ellipsis: true,
                    render: (h, params) => {
                        return h('span', {
                            attrs: {
                                title: params.row.pluginName
                            }
                        }, params.row.pluginName);
                    }
                }
            ],
            rColumns: [
                {
                    title: '管理功能',
                    width: 150,
                    key: 'name',
                    ellipsis: true,
                    render: (h, params) => {
                        return h('span', {
                            attrs: {
                                title: params.row.name
                            }
                        }, params.row.name);
                    }
                },
                {
                    width: 115,
                    title: '节点',
                    key: 'instanceName',
                    ellipsis: true,
                    render: (h, params) => {
                        return h('span', {
                            attrs: {
                                title: params.row.instanceName
                            }
                        }, params.row.instanceName);
                    }
                },
                {
                    width: 110,
                    title: '插件',
                    key: 'pluginName',
                    ellipsis: true,
                    render: (h, params) => {
                        return h('span', {
                            attrs: {
                                title: params.row.pluginName
                            }
                        }, params.row.pluginName);
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    fixed: 'right',
                    width: 70,
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.removeSignleRightData(params.row);
                                        }
                                    }
                                },
                                '移除'
                            )
                        ]);
                    }
                }
            ],
            isLoading: false,
            resultList: [],
            leftData: [],
            rightData: [],
            exportLoading: false,
            loadingLeft: false,
            taskId: '',
            exportStatus: '',
            lastExportTime: '',
            exportErrorMsg: '',
            statusDesc: {
                RUNNING: '正在导出',
                STOPPED: '导出停止',
                SUCCESS: '导出成功',
                FAILED: '导出失败'
            },
            filePath: '',
            process: 0,
            timer: null
        };
    },
    methods: {
        // 首次打开
        handleOpen() {
            this.modalInfo = this.modalData;
            // 导出进度查询
            this.getManageExportProgress();
            this.timer && window.clearInterval(this.timer);
            this.timer = setInterval(() => {
                this.getManageExportProgress();
            }, 3000);
            // 查询所有管理功能节点数据
            this.getManageInfoAllProduct();
        },
        // 搜索
        handleSearch() {
            if (!this.selectManageName) {
                this.$hMessage.warning('请选择管理功能后查询!');
                return;
            }
            let filteredArray = this.resultList;
            if (this.selectManageName) {
                filteredArray = filteredArray.filter(fun =>
                    String(fun.functionName).toLowerCase().includes(String(this.selectManageName).toLowerCase())
                );
            }
            if (this.selectInstanceList.length) {
                filteredArray = _.filter(filteredArray, (obj) => {
                    return _.includes(this.selectInstanceList, obj.instanceName);
                });
            }
            this.leftData = filteredArray;
        },
        handleSearchChange() {
            this.selectInstanceList = [];
        },
        filterArray(arrA, arrB) {
            return arrA.filter(itemA => {
                return !arrB.some(itemB => {
                    return Object.keys(itemB).every(key => {
                        return itemA['functionName'] === itemB['functionName'] &&
                            itemA['pluginName'] === itemB['pluginName'] &&
                            itemA['instanceId'] === itemB['instanceId'];
                    });
                });
            });
        },
        // 过滤同一个数组对象中重复元素
        filterDuplicateItems(arr) {
            const seen = new Set();
            return arr.filter(item => {
                const key = `${item.functionName}-${item.instanceName}-${item.pluginName}`;
                if (!seen.has(key)) {
                    seen.add(key);
                    return true;
                }
                return false;
            });
        },
        // 获取管理功能列表数据
        async getManageInfoAllProduct() {
            this.loadingLeft = true;
            try {
                const res = await getManageInfoAllProduct({
                    productId: this.productId
                });
                if (res.success) {
                    this.resultList = res.data;
                }
            } catch (error) {
                this.resultList = [];
                console.log(error);
            } finally {
                this.loadingLeft = false;
                const uniqueValues = [...new Set(this.resultList.map(item => item.name).filter(value => value !== undefined))];
                this.remoteData = uniqueValues.map(value => ({
                    label: value,
                    value: value
                }));
            }
        },
        // 转移时事件监听
        handleTransferChange(rightData, direction, moveData) {
            this.rightData = this.filterDuplicateItems(rightData);
            const data = _.cloneDeep(this.leftData);
            this.leftData = data;
        },
        // 查询管理功能导出进度
        async getManageExportProgress() {
            try {
                const res = await getManageExportProgress({
                    productId: this.productId
                });
                if (res.success) {
                    this.exportStatus = res?.data?.status;
                    this.lastExportTime = res?.data?.createTime;
                    this.filePath = res?.data?.filePath;
                    this.taskId = res?.data?.taskId;
                    this.exportErrorMsg = res?.data?.errorMsg;
                    this.process = Math.ceil((res?.data?.completedRequests || 0) / (res?.data?.totalRequests || 1) * 100);
                }
            } catch (error) {
                console.log(error);
                this.$hMessage.error('导出进度查询异常，请联系管理员');
                this.timer && window.clearInterval(this.timer);
            }

        },
        // 停止导出
        async handleStopExport() {
            try {
                const res = await stopDownloadManage({
                    productId: this.productId,
                    taskId: this.taskId
                });
                if (res.success) {
                    this.$hMessage.success('终止成功');
                    this.getManageExportProgress();
                } else {
                    this.$hMessage.error(res.message || '终止失败');
                }
            } catch (error) {
                this.$hMessage.error('导出终止异常');
                console.log(error);
            }

        },
        // 开始导出
        async submitConfig() {
            try {
                this.exportLoading = true;
                const res = await exportManageApi(JSON.stringify({
                    productId: this.productId,
                    apiBriefInfos: this.rightData
                }));
                if (res.success) {
                    this.$hMessage.success('导出任务启动成功，请稍候');
                    this.getManageExportProgress();
                    // 导出成功后，所有数据重置
                    this.rightData = [];
                    this.selectInstanceList = [];
                    this.selectManageName = '';
                    this.leftData = [];
                } else {
                    this.$hMessage.error('导出任务启动失败');
                }
            } catch (error) {
                this.$hMessage.error('导出失败');
                console.error(error);
            } finally {
                this.exportLoading = false;
            }
        },
        // 清空导出列表
        clearRightData() {
            this.rightData = [];
        },
        // 删除某个导出元素
        removeSignleRightData(data) {
            const idx = _.findIndex(this.rightData, {
                functionName: data.functionName,
                instanceId: data.instanceId,
                pluginName: data.pluginName
            });
            this.rightData.splice(idx, 1);
        },
        // 文件下载
        async downloadManageFile() {
            if (!this.filePath) return;

            const res = await downloadManageFile({
                path: this.filePath
            });
            // 创建一个 Blob 对象
            const blob = new Blob([res]);

            // 创建一个临时的 URL
            const url = URL.createObjectURL(blob);

            // 创建一个 a 标签
            const link = document.createElement('a');
            link.href = url;

            // 设置文件名，需要根据实际情况修改文件名
            link.download = this.filePath.split('/').pop() || `manageApi_export_${this.lastExportTime}.zip`;

            // 模拟点击 a 标签来触发下载
            link.click();

            // 释放临时 URL
            URL.revokeObjectURL(url);
        }
    },
    beforeDestroy() {
        this.timer && window.clearInterval(this.timer);
    },
    computed: {
        instanceList() {
            if (this.selectManageName) {
                const filteredList = _.filter(this.resultList, o => {
                    return o.functionName === this.selectManageName;
                });

                const uniqueList = [];
                const seenIds = new Set();

                filteredList.forEach(item => {
                    if (!seenIds.has(item.instanceId)) {
                        seenIds.add(item.instanceId);
                        uniqueList.push(item);
                    }
                });

                return uniqueList;
            }
            return [];
        }
    },
    components: { aButton }
};
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 0 16px 16px;
}

/deep/ .h-table-wrapper {
    margin: 10px 10px 0;
}

.batch-box {
    position: relative;
    margin-top: 10px;
    width: 100%;
    height: 350px;

    .batch-box-title {
        display: flex;
        margin-bottom: 8px;

        .batch-title {
            font-size: 14px;
            color: #333;
            margin-left: 10px;

            & > span {
                font-size: 12px;
                color: #8b8b8b;
            }

            &::before {
                display: inline-block;
                position: relative;
                left: -7px;
                top: 4px;
                content: "";
                width: 5px;
                height: 17px;
                background: var(--link-color);
            }
        }

        .btach-search {
            margin: 10px 0 0 2px;
        }
    }

    /deep/ .h-transfer-table-operation {
        & > button:nth-of-type(1) {
            display: none;
        }

        & > button:nth-of-type(3) {
            display: none;
        }

        & > button:nth-of-type(4) {
            display: none;
        }
    }

    /deep/ .h-transfer-table-list-body {
        border-radius: 0;
    }
}

.batch-box-left {
    width: 50%;
}

.batch-box-right {
    width: calc(50% - 35px);
    margin-left: 25px;

    .export-run-box {
        display: inline-block;
        margin: 10px 0 0 2px;
        position: relative;
        width: 250px;
        height: 32px;
        border-radius: 4px;
        border: 1px solid #ccc;
        color: #333;

        &::before {
            content: "";
            display: inline-block;
            width: 6px;
            height: 6px;
            border-radius: 4px;
            position: absolute;
            left: 10px;
            top: 12px;
        }

        & > span {
            line-height: 30px;
        }

        .export-run-text {
            margin: 0 6px 0 24px;
        }

        .export-run-opt {
            margin-left: 6px;
        }
    }

    .export-run-box-SUCCESS::before {
        background-color: #3b990c;
    }

    .export-run-box-FAILED::before {
        background-color: red;
    }

    .export-run-box-STOPPED::before {
        background-color: #ccc;
    }

    .export-run-box-RUNNING::before {
        background-color: #ffb84f;
    }
}

</style>
