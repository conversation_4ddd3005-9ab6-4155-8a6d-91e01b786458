<!--
 * @Description: 核心数据同步
-->
<template>
    <div class="main">
        <header>
            <a-title title="核心数据同步">
                <slot>
                    <h-select
                        v-show="productList.length > 1"
                        v-model="productInstNo"
                        class="title-single-select"
                        placeholder="请选择"
                        :positionFixed="true"
                        :clearable="false"
                        @on-change="checkProduct">
                        <h-option
                            v-for="item in productList"
                            :key="item.id"
                            :value="item.productInstNo"
                            >{{ item.productName }}</h-option>
                    </h-select>
                </slot>
            </a-title>
        </header>
        <div ref="tab-wrapper" class="tab-wrapper">
            <!-- 集群数据同步差量组件 -->
            <data-accord-observation
                ref="data-accord"
                :productInstNo="productInstNo"
                />
        </div>
    </div>
</template>
<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import aTitle from '@/components/common/title/aTitle';
import dataAccordObservation from '@/components/accordObservation/dataAccordObservation';
export default {
    components: { aTitle, dataAccordObservation },
    data() {
        return {
            productInstNo: ''
        };
    },
    mounted() {
        this.init();
    },
    beforeDestroy() {
        this.clearPolling();
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight;
            }
        })
    },
    watch: {
        productInstNo() {
            this.clearPolling();
            this.$nextTick(async () => {
                this.$refs?.['data-accord'] && this.$refs['data-accord'].init();
            });
        }
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        async init(){
            await this.getProductList({ filter: 'excludeLdpApm' });
            const productInstNo = localStorage.getItem('productInstNo');
            this.productInstNo =  _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo ||
            this.productList?.[0]?.productInstNo;
        },
        // 切换产品
        async checkProduct(productId) {
            if (!this.productList.length) return;
            this.productInfo = productId ? _.find(this.productList, ['productInstNo', productId]) : this.productList[0];
            this.productInstNo = this.productInfo.productInstNo;
            localStorage.setItem('productInstNo', this.productInfo.productInstNo);
        },
        // 定时器
        setPolling(timerInterval) {
            this.clearPolling();
            this.timer = setInterval(() => {
                this.handleShardTabs();
            }, (timerInterval) * 1000);
        },
        // 清空定时器
        clearPolling() {
            this.$refs?.['data-accord'] && this.$refs['data-accord'].clearPolling();
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/tab.less");

.tab-wrapper {
    position: relative;
    width: 100%;
    margin-top: 6px;
    height: calc(100% - 51px);
}
</style>
