/**
 * @Description: 注册信息模拟数据
 * @Author: Generated
 * @Date: 2025-08-15
 */

// 生成模拟的注册信息数据
export const generateRegistrationData = (count = 45) => {
    const data = [];
    const ips = ['**********', '**********', '**********', '**********', '**********'];
    const ports = [8088, 8089, 8090, 8091, 8092];
    const shards = [61, 62, 63];
    const statuses = ['online', 'offline'];
    const nodeTypes = ['core', 'gateway', 'worker', 'monitor'];
    const versions = ['1.0.0', '1.0.1', '1.1.0', '2.0.0'];
    
    for (let i = 1; i <= count; i++) {
        const nodeId = `121201${String(i).padStart(3, '0')}`;
        const ip = ips[Math.floor(Math.random() * ips.length)];
        const port = ports[Math.floor(Math.random() * ports.length)];
        const shardId = shards[Math.floor(Math.random() * shards.length)];
        const healthStatus = i <= 40 ? 'online' : statuses[Math.floor(Math.random() * statuses.length)];
        const nodeType = nodeTypes[Math.floor(Math.random() * nodeTypes.length)];
        const version = versions[Math.floor(Math.random() * versions.length)];
        
        // 生成时间
        const registerTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000); // 30天内随机时间
        const lastHeartbeatTime = healthStatus === 'online' 
            ? new Date(Date.now() - Math.random() * 60 * 1000) // 1分钟内
            : new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000); // 24小时内
        
        data.push({
            nodeId,
            ip,
            port,
            shardId,
            healthStatus,
            lastHeartbeatTime: lastHeartbeatTime.toISOString(),
            registerTime: registerTime.toISOString(),
            nodeType,
            version,
            weight: Math.floor(Math.random() * 100) + 1,
            maxConnections: Math.floor(Math.random() * 1000) + 100,
            currentConnections: Math.floor(Math.random() * 500),
            cpuUsage: Math.floor(Math.random() * 100),
            memoryUsage: Math.floor(Math.random() * 100),
            requestCount: Math.floor(Math.random() * 10000),
            remark: i % 5 === 0 ? `节点${nodeId}的备注信息` : '',
            tags: i % 3 === 0 ? 'production,stable' : i % 3 === 1 ? 'test,debug' : 'development'
        });
    }
    
    return data;
};

// 模拟API响应
export const mockRegistrationListResponse = (params = {}) => {
    const { 
        pageNum = 1, 
        pageSize = 10, 
        nodeId = '', 
        ip = '', 
        port = '', 
        shardId = '', 
        healthStatus = '' 
    } = params;
    
    let allData = generateRegistrationData();
    
    // 过滤数据
    if (nodeId) {
        allData = allData.filter(item => item.nodeId.includes(nodeId));
    }
    if (ip) {
        allData = allData.filter(item => item.ip.includes(ip));
    }
    if (port) {
        allData = allData.filter(item => item.port.toString().includes(port));
    }
    if (shardId) {
        allData = allData.filter(item => item.shardId.toString() === shardId);
    }
    if (healthStatus) {
        allData = allData.filter(item => item.healthStatus === healthStatus);
    }
    
    // 分页
    const total = allData.length;
    const startIndex = (pageNum - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const list = allData.slice(startIndex, endIndex);
    
    return {
        code: '200',
        message: 'success',
        data: {
            list,
            total,
            pageNum,
            pageSize,
            totalPages: Math.ceil(total / pageSize)
        }
    };
};

// 模拟导出API响应
export const mockExportResponse = () => {
    // 生成CSV格式的数据
    const data = generateRegistrationData();
    const headers = ['节点ID', 'IP', '端口', '所属分片', '健康状态', '最后心跳时间', '注册时间', '节点类型', '版本号'];
    
    let csvContent = headers.join(',') + '\n';
    data.forEach(item => {
        const row = [
            item.nodeId,
            item.ip,
            item.port,
            item.shardId,
            item.healthStatus === 'online' ? '在线' : '离线',
            item.lastHeartbeatTime,
            item.registerTime,
            item.nodeType,
            item.version
        ];
        csvContent += row.join(',') + '\n';
    });
    
    return {
        code: '200',
        message: 'success',
        data: csvContent
    };
};

// 模拟ping测试响应
export const mockPingResponse = (nodeId) => {
    const isSuccess = Math.random() > 0.1; // 90%成功率
    
    return {
        code: isSuccess ? '200' : '500',
        message: isSuccess ? 'success' : 'connection failed',
        data: {
            nodeId,
            status: isSuccess ? 'connected' : 'failed',
            responseTime: isSuccess ? Math.floor(Math.random() * 100) + 10 : null,
            timestamp: new Date().toISOString()
        }
    };
};
