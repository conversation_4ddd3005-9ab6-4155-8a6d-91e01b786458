# 注册信息页面使用说明

## 概述

本页面基于项目中现有的 `queryTable` 组件开发，用于展示和管理节点注册信息。页面包含查询筛选、数据展示、详情查看、导出等功能。

## 文件结构

```
src/
├── views/index/registrationInfo.vue          # 主页面组件
├── components/registrationInfo/
│   └── detailModal.vue                       # 详情弹窗组件
├── api/httpApi.js                           # API接口（已添加注册信息相关接口）
├── mock/registrationData.js                 # 模拟数据
└── router/router.js                         # 路由配置（已添加）
```

## 功能特性

### 1. 查询筛选功能
- **节点ID筛选**：支持模糊查询
- **IP地址筛选**：支持模糊查询
- **端口筛选**：支持模糊查询
- **所属分片筛选**：下拉选择
- **健康状态筛选**：在线/离线状态选择

### 2. 数据展示
- **表格展示**：使用 queryTable 组件展示数据
- **分页功能**：支持分页浏览
- **状态标识**：在线/离线状态用不同颜色的圆点标识
- **时间格式化**：心跳时间和注册时间格式化显示

### 3. 操作功能
- **详情查看**：点击节点ID或详情按钮查看节点详细信息
- **测试连接**：测试节点连接状态
- **数据刷新**：手动刷新数据
- **数据导出**：导出当前查询结果

### 4. 详情弹窗
详情弹窗包含以下信息模块：
- **基本信息**：节点ID、IP、端口、分片
- **状态信息**：健康状态、心跳时间、注册时间、运行时长
- **配置信息**：节点类型、版本号、权重、最大连接数
- **性能指标**：CPU使用率、内存使用率、当前连接数、处理请求数
- **扩展信息**：备注、标签

## 使用方法

### 1. 访问页面
在浏览器中访问：`/registrationInfo`

### 2. 查询数据
1. 在筛选条件区域输入查询条件
2. 点击"查询"按钮执行查询
3. 点击"重置"按钮清空查询条件

### 3. 查看详情
- 点击表格中的节点ID（蓝色链接）
- 或点击操作列中的"详情"按钮

### 4. 测试连接
点击操作列中的"测试连接"按钮，系统会测试与该节点的连接状态

### 5. 导出数据
点击右上角的"导出"按钮，系统会导出当前查询结果为Excel文件

## 技术实现

### 1. 组件依赖
- `queryTable`：主表格组件
- `aButton`：按钮组件
- `detailModal`：详情弹窗组件

### 2. API接口
- `getRegistrationList(param)`：获取注册信息列表
- `exportRegistrationData(param)`：导出注册信息
- `pingNode(param)`：测试节点连接

### 3. 模拟数据
在开发环境中，API会自动使用模拟数据，包含45个节点的示例数据。

### 4. 样式特性
- 响应式布局
- 暗色主题适配
- 状态颜色标识
- 悬浮提示

## 自定义配置

### 1. 修改查询条件
在 `registrationInfo.vue` 的 `formItems` 数组中修改查询表单配置：

```javascript
formItems: [
    {
        type: 'input',
        key: 'nodeId',
        label: '节点ID',
        placeholder: '请输入节点ID'
    },
    // 添加更多查询条件...
]
```

### 2. 修改表格列
在 `columnData` 数组中修改表格列配置：

```javascript
columnData: [
    {
        title: '节点ID',
        key: 'nodeId',
        width: 120,
        align: 'center'
    },
    // 添加更多列...
]
```

### 3. 修改模拟数据
在 `src/mock/registrationData.js` 中修改 `generateRegistrationData` 函数来自定义模拟数据。

## 注意事项

1. **环境配置**：模拟数据仅在开发环境中生效，生产环境会调用真实API
2. **权限控制**：如需添加权限控制，可在路由配置中添加相应的权限验证
3. **数据格式**：确保后端API返回的数据格式与前端期望的格式一致
4. **错误处理**：页面已包含基本的错误处理，可根据需要进行扩展

## 扩展建议

1. **批量操作**：可添加批量删除、批量测试连接等功能
2. **实时监控**：可添加WebSocket连接实现实时状态更新
3. **图表展示**：可添加节点状态分布图、性能趋势图等
4. **日志查看**：可添加节点日志查看功能
5. **配置管理**：可添加节点配置修改功能
